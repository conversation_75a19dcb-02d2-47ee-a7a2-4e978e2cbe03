"""
CMS业务逻辑服务
"""
import logging
import re
import requests
from typing import Optional, List, Dict, Any, Tu<PERSON>
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, asc

from .database import Article, get_db_session
from .models import ArticleCreate, ArticleUpdate, SyncResponse

logger = logging.getLogger(__name__)

class CMSService:
    """CMS服务类"""
    
    def __init__(self):
        self.rag_base_url = "http://localhost:9000"
    
    def get_articles(
        self, 
        db: Session,
        page: int = 1, 
        page_size: int = 20,
        search: Optional[str] = None,
        category: Optional[str] = None,
        status: Optional[str] = None
    ) -> Tuple[List[Article], int]:
        """获取文章列表"""
        query = db.query(Article).filter(Article.is_deleted == False)
        
        # 搜索过滤
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                Article.title.like(search_term) |
                Article.content.like(search_term) |
                Article.summary.like(search_term)
            )
        
        # 分类过滤
        if category:
            query = query.filter(Article.category == category)
        
        # 状态过滤
        if status:
            query = query.filter(Article.status == status)
        
        # 获取总数
        total = query.count()
        
        # 分页和排序
        articles = query.order_by(desc(Article.updated_at)).offset(
            (page - 1) * page_size
        ).limit(page_size).all()
        
        return articles, total
    
    def get_article_by_id(self, db: Session, article_id: int) -> Optional[Article]:
        """根据ID获取文章"""
        return db.query(Article).filter(
            Article.id == article_id,
            Article.is_deleted == False
        ).first()
    
    def create_article(self, db: Session, article_data: ArticleCreate) -> Article:
        """创建文章"""
        article = Article(**article_data.dict())
        article.created_at = datetime.utcnow()
        article.updated_at = datetime.utcnow()
        
        db.add(article)
        db.commit()
        db.refresh(article)
        
        logger.info(f"创建文章: {article.title} (ID: {article.id})")
        return article
    
    def update_article(
        self, 
        db: Session, 
        article_id: int, 
        article_data: ArticleUpdate
    ) -> Optional[Article]:
        """更新文章"""
        article = self.get_article_by_id(db, article_id)
        if not article:
            return None
        
        update_data = article_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(article, field, value)
        
        article.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(article)
        
        logger.info(f"更新文章: {article.title} (ID: {article.id})")
        return article
    
    def delete_article(self, db: Session, article_id: int) -> bool:
        """删除文章（软删除）"""
        article = self.get_article_by_id(db, article_id)
        if not article:
            return False
        
        article.is_deleted = True
        article.updated_at = datetime.utcnow()
        db.commit()
        
        logger.info(f"删除文章: {article.title} (ID: {article.id})")
        return True
    
    def get_articles_to_add(self, db: Session) -> List[Dict[str, Any]]:
        """获取需要添加到RAG的文章"""
        try:
            # 获取RAG中已有的文档
            response = requests.get(f"{self.rag_base_url}/api/documents")
            if response.status_code != 200:
                logger.error(f"获取RAG文档列表失败: {response.status_code}")
                return []
            
            rag_documents = response.json()
            rag_filenames = {doc.get("filename", "") for doc in rag_documents}
            
            # 获取数据库中的文章
            articles = db.query(Article).filter(
                Article.is_deleted == False,
                Article.status == "published"
            ).all()
            
            articles_to_add = []
            for article in articles:
                filename = f"{article.title}.txt"
                if filename not in rag_filenames:
                    articles_to_add.append({
                        "content_id": article.id,  # 前端期望的字段名
                        "id": article.id,
                        "title": article.title,
                        "content": article.content or "",
                        "filename": filename
                    })
            
            return articles_to_add
            
        except Exception as e:
            logger.error(f"获取待添加文章失败: {e}")
            return []
    
    def get_articles_to_delete(self, db: Session) -> List[str]:
        """获取需要从RAG删除的文档"""
        try:
            # 获取RAG中的文档
            response = requests.get(f"{self.rag_base_url}/api/documents")
            if response.status_code != 200:
                logger.error(f"获取RAG文档列表失败: {response.status_code}")
                return []
            
            rag_documents = response.json()
            
            # 获取数据库中有效的文章标题
            articles = db.query(Article.title).filter(
                Article.is_deleted == False,
                Article.status == "published"
            ).all()
            
            valid_filenames = {f"{article.title}.txt" for article in articles}
            
            # 找出需要删除的文档
            documents_to_delete = []
            for doc in rag_documents:
                filename = doc.get("filename", "")
                if filename.endswith(".txt") and filename not in valid_filenames:
                    documents_to_delete.append(filename)
            
            return documents_to_delete
            
        except Exception as e:
            logger.error(f"获取待删除文档失败: {e}")
            return []
    
    def html_to_text(self, html_content: str) -> str:
        """将HTML内容转换为纯文本"""
        if not html_content:
            return ""

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', html_content)

        # 解码HTML实体
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&amp;', '&')
        text = text.replace('&quot;', '"')

        # 清理多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()

        return text

    def get_article_txt(self, db: Session, article_id: int) -> Optional[str]:
        """获取文章的TXT内容"""
        article = self.get_article_by_id(db, article_id)
        if not article:
            return None

        # 如果有正文内容，转换为纯文本
        if article.content and article.content.strip():
            return self.html_to_text(article.content)

        # 如果没有正文但有source_url，返回URL作为内容
        if article.source_url and article.source_url.strip():
            return f"原文链接: {article.source_url}\n\n文章标题: {article.title}"

        # 如果都没有，返回标题作为内容
        return f"文章标题: {article.title}\n\n注意: 该文章暂无正文内容"
    
    def sync_article_to_rag(self, article_data: Dict[str, Any]) -> bool:
        """同步单个文章到RAG"""
        try:
            # 准备文档数据
            content = self.html_to_text(article_data.get("content", ""))
            if not content.strip():
                logger.warning(f"文章内容为空，跳过同步: {article_data['title']}")
                return False
            
            # 调用RAG API上传文档
            files = {
                "file": (
                    article_data["filename"],
                    content.encode('utf-8'),
                    "text/plain"
                )
            }
            
            response = requests.post(
                f"{self.rag_base_url}/api/documents/upload",
                files=files,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"文章同步成功: {article_data['title']}")
                return True
            else:
                logger.error(f"文章同步失败: {article_data['title']}, 状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"同步文章到RAG失败: {article_data['title']}, 错误: {e}")
            return False
    
    def delete_document_from_rag(self, filename: str) -> bool:
        """从RAG删除文档"""
        try:
            response = requests.delete(
                f"{self.rag_base_url}/api/documents/{filename}",
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"文档删除成功: {filename}")
                return True
            else:
                logger.error(f"文档删除失败: {filename}, 状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"删除RAG文档失败: {filename}, 错误: {e}")
            return False
    
    def sync_articles(self, db: Session, force: bool = False) -> SyncResponse:
        """同步文章到RAG系统"""
        try:
            added_count = 0
            deleted_count = 0
            error_count = 0
            
            # 获取需要添加的文章
            articles_to_add = self.get_articles_to_add(db)
            for article in articles_to_add:
                if self.sync_article_to_rag(article):
                    added_count += 1
                else:
                    error_count += 1
            
            # 获取需要删除的文档
            documents_to_delete = self.get_articles_to_delete(db)
            for filename in documents_to_delete:
                if self.delete_document_from_rag(filename):
                    deleted_count += 1
                else:
                    error_count += 1
            
            success = error_count == 0
            message = f"同步完成: 添加 {added_count} 篇，删除 {deleted_count} 篇"
            if error_count > 0:
                message += f"，失败 {error_count} 个"
            
            return SyncResponse(
                success=success,
                message=message,
                added_count=added_count,
                deleted_count=deleted_count,
                error_count=error_count
            )
            
        except Exception as e:
            logger.error(f"同步文章失败: {e}")
            return SyncResponse(
                success=False,
                message=f"同步失败: {str(e)}",
                error_count=1
            )

# 全局CMS服务实例
cms_service = CMSService()
