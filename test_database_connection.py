#!/usr/bin/env python3
"""
测试数据库连接和数据获取脚本
用于验证能否正确从本地MySQL数据库chestnut_cms获取信息
"""

import sys
import logging
from datetime import datetime
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接"""
    try:
        logger.info("=" * 60)
        logger.info("开始测试数据库连接")
        logger.info("=" * 60)
        
        # 1. 测试PyMySQL模块
        logger.info("1. 检查PyMySQL模块...")
        try:
            import pymysql
            logger.info("✓ PyMySQL模块已安装")
        except ImportError as e:
            logger.error(f"✗ PyMySQL模块未安装: {e}")
            return False
        
        # 2. 测试SQLAlchemy
        logger.info("2. 检查SQLAlchemy模块...")
        try:
            from sqlalchemy import create_engine, text
            from sqlalchemy.orm import sessionmaker
            logger.info("✓ SQLAlchemy模块已安装")
        except ImportError as e:
            logger.error(f"✗ SQLAlchemy模块未安装: {e}")
            return False
        
        # 3. 数据库配置
        logger.info("3. 配置数据库连接...")
        db_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '5Secsgo100',
            'database': 'chestnut_cms',
            'charset': 'utf8mb4'
        }
        
        logger.info(f"   主机: {db_config['host']}")
        logger.info(f"   端口: {db_config['port']}")
        logger.info(f"   用户: {db_config['user']}")
        logger.info(f"   密码: {'*' * len(db_config['password'])}")
        logger.info(f"   数据库: {db_config['database']}")
        
        # 4. 创建数据库连接
        logger.info("4. 创建数据库连接...")
        database_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        
        engine = create_engine(database_url, echo=False)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # 5. 测试连接
        logger.info("5. 测试数据库连接...")
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            test_value = result.fetchone()[0]
            logger.info(f"✓ 数据库连接成功，测试查询返回: {test_value}")
        
        # 6. 测试数据库和表
        logger.info("6. 检查数据库和表结构...")
        with engine.connect() as connection:
            # 检查数据库
            result = connection.execute(text("SELECT DATABASE() as current_db"))
            current_db = result.fetchone()[0]
            logger.info(f"   当前数据库: {current_db}")
            
            # 检查表
            result = connection.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            logger.info(f"   数据库中的表: {tables}")
            
            # 检查articles表是否存在
            if 'articles' in tables:
                logger.info("✓ articles表存在")
                
                # 获取表结构
                result = connection.execute(text("DESCRIBE articles"))
                columns = result.fetchall()
                logger.info("   articles表结构:")
                for column in columns:
                    logger.info(f"     {column[0]} - {column[1]} - {column[2]} - {column[3]}")
                
                # 获取数据统计
                result = connection.execute(text("SELECT COUNT(*) as total FROM articles"))
                total_count = result.fetchone()[0]
                logger.info(f"   articles表总记录数: {total_count}")
                
                if total_count > 0:
                    # 获取已发布文章数量
                    result = connection.execute(text("SELECT COUNT(*) as published FROM articles WHERE status = 'published' AND is_deleted = 0"))
                    published_count = result.fetchone()[0]
                    logger.info(f"   已发布文章数量: {published_count}")
                    
                    # 获取前5篇文章示例
                    result = connection.execute(text("SELECT id, title, status, is_deleted, created_at FROM articles ORDER BY id DESC LIMIT 5"))
                    articles = result.fetchall()
                    logger.info("   最新5篇文章:")
                    for article in articles:
                        logger.info(f"     ID: {article[0]}, 标题: {article[1]}, 状态: {article[2]}, 删除: {article[3]}, 创建时间: {article[4]}")
                
            else:
                logger.warning("✗ articles表不存在")
        
        logger.info("=" * 60)
        logger.info("数据库连接测试完成")
        logger.info("=" * 60)
        return True
        
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return False

def test_cms_service():
    """测试CMS服务功能"""
    try:
        logger.info("\n" + "=" * 60)
        logger.info("开始测试CMS服务功能")
        logger.info("=" * 60)
        
        # 导入CMS相关模块
        sys.path.append('backend/app')
        from backend.app.cms.database import get_db_session, Article
        from backend.app.cms.service import CMSService
        
        # 创建CMS服务实例
        cms_service = CMSService()
        logger.info("✓ CMS服务实例创建成功")
        
        # 获取数据库会话
        db = get_db_session()
        if db is None:
            logger.error("✗ 无法获取数据库会话")
            return False
        
        logger.info("✓ 数据库会话获取成功")
        
        try:
            # 测试获取文章列表
            logger.info("1. 测试获取文章列表...")
            articles, total = cms_service.get_articles(db, page=1, page_size=5)
            logger.info(f"   获取到 {len(articles)} 篇文章，总数: {total}")
            
            for article in articles:
                logger.info(f"   - ID: {article.id}, 标题: {article.title}, 状态: {article.status}")
            
            # 测试获取待添加文章
            logger.info("2. 测试获取待添加文章...")
            articles_to_add = cms_service.get_articles_to_add(db)
            logger.info(f"   需要添加到RAG的文章数量: {len(articles_to_add)}")
            
            for article in articles_to_add[:3]:  # 只显示前3个
                logger.info(f"   - ID: {article.get('content_id')}, 标题: {article.get('title')}")
            
            # 测试获取待删除文档
            logger.info("3. 测试获取待删除文档...")
            docs_to_delete = cms_service.get_articles_to_delete(db)
            logger.info(f"   需要从RAG删除的文档数量: {len(docs_to_delete)}")
            
            if docs_to_delete:
                for doc in docs_to_delete[:3]:  # 只显示前3个
                    logger.info(f"   - 文档: {doc}")
            
            logger.info("✓ CMS服务功能测试完成")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"CMS服务测试失败: {e}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("开始数据库连接和CMS服务测试")
    logger.info(f"测试时间: {datetime.now()}")
    
    # 测试数据库连接
    db_success = test_database_connection()
    
    if db_success:
        # 测试CMS服务
        cms_success = test_cms_service()
        
        if cms_success:
            logger.info("\n🎉 所有测试通过！数据库连接和CMS服务工作正常。")
        else:
            logger.error("\n❌ CMS服务测试失败")
    else:
        logger.error("\n❌ 数据库连接测试失败")

if __name__ == "__main__":
    main()
