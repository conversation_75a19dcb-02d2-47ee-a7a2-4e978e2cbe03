#!/usr/bin/env python3
"""
创建测试数据脚本
在chestnut_cms数据库的articles表中插入一些测试文章
"""

import sys
import logging
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def create_test_articles():
    """创建测试文章数据"""
    try:
        logger.info("=" * 60)
        logger.info("开始创建测试文章数据")
        logger.info("=" * 60)
        
        # 数据库配置
        db_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '5Secsgo100',
            'database': 'chestnut_cms',
            'charset': 'utf8mb4'
        }
        
        # 创建数据库连接
        database_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}"
        engine = create_engine(database_url, echo=False)
        
        # 测试文章数据
        test_articles = [
            {
                'title': '百链开通试用了！',
                'content': '百链是全国图书馆资源的联合搜索，拥有7.2亿条元数据，通过410个中外文数据库中获取元数据，其中收录中文期刊11000万篇元数据，外文期刊28000万篇元数据。此外，还可以通过文献传递方式获取到图书馆中没有的中外文文献资料。目前已在全国建立联合分中心70个，应用的图书馆超过1000多家，每天超过100万人次在这里获取学术资源。',
                'summary': '百链图书馆资源联合搜索平台开通试用通知',
                'author': '图书馆管理员',
                'category': '通知公告',
                'tags': '百链,图书馆,资源搜索',
                'status': 'published',
                'source_url': 'https://example.com/bailink-notice'
            },
            {
                'title': '数字图书馆使用指南',
                'content': '数字图书馆是现代图书馆服务的重要组成部分，为读者提供24小时不间断的数字资源访问服务。本指南将详细介绍如何使用数字图书馆的各项功能，包括资源检索、在线阅读、文献下载等。',
                'summary': '详细介绍数字图书馆的使用方法和功能',
                'author': '技术部',
                'category': '使用指南',
                'tags': '数字图书馆,使用指南,在线阅读',
                'status': 'published',
                'source_url': 'https://example.com/digital-library-guide'
            },
            {
                'title': '学术资源检索技巧',
                'content': '在进行学术研究时，高效的资源检索技巧至关重要。本文将介绍如何使用关键词、布尔逻辑、字段检索等方法来提高检索效率，帮助研究者快速找到所需的学术资源。',
                'summary': '介绍学术资源检索的方法和技巧',
                'author': '学科馆员',
                'category': '学术指导',
                'tags': '学术检索,研究方法,关键词',
                'status': 'published',
                'source_url': 'https://example.com/search-tips'
            },
            {
                'title': '图书馆新书推荐',
                'content': '本月图书馆新增了一批优质图书，涵盖文学、科技、历史等多个领域。这些新书不仅内容丰富，而且具有很高的学术价值和实用性。欢迎广大读者前来借阅。',
                'summary': '本月新书推荐列表',
                'author': '采编部',
                'category': '新书推荐',
                'tags': '新书,推荐,借阅',
                'status': 'published',
                'source_url': 'https://example.com/new-books'
            },
            {
                'title': '图书馆开放时间调整通知',
                'content': '由于系统维护需要，图书馆将在本周末进行开放时间调整。具体安排如下：周六上午9:00-12:00正常开放，下午闭馆进行系统维护；周日全天正常开放。给读者带来的不便，敬请谅解。',
                'summary': '图书馆周末开放时间调整通知',
                'author': '管理部',
                'category': '通知公告',
                'tags': '开放时间,调整,维护',
                'status': 'published',
                'source_url': 'https://example.com/schedule-notice'
            }
        ]
        
        # 插入测试数据
        with engine.connect() as connection:
            # 先检查是否已有数据
            result = connection.execute(text("SELECT COUNT(*) FROM articles"))
            existing_count = result.fetchone()[0]
            
            if existing_count > 0:
                logger.info(f"数据库中已有 {existing_count} 篇文章")
                user_input = input("是否要清空现有数据并重新插入测试数据？(y/N): ")
                if user_input.lower() != 'y':
                    logger.info("取消操作")
                    return
                
                # 清空现有数据
                connection.execute(text("DELETE FROM articles"))
                connection.commit()
                logger.info("已清空现有数据")
            
            # 插入测试文章
            current_time = datetime.now()
            
            for i, article in enumerate(test_articles, 1):
                insert_sql = text("""
                    INSERT INTO articles (
                        title, content, summary, author, category, tags, 
                        status, created_at, updated_at, published_at, 
                        is_deleted, source_url
                    ) VALUES (
                        :title, :content, :summary, :author, :category, :tags,
                        :status, :created_at, :updated_at, :published_at,
                        :is_deleted, :source_url
                    )
                """)
                
                connection.execute(insert_sql, {
                    'title': article['title'],
                    'content': article['content'],
                    'summary': article['summary'],
                    'author': article['author'],
                    'category': article['category'],
                    'tags': article['tags'],
                    'status': article['status'],
                    'created_at': current_time,
                    'updated_at': current_time,
                    'published_at': current_time,
                    'is_deleted': False,
                    'source_url': article['source_url']
                })
                
                logger.info(f"✓ 插入文章 {i}: {article['title']}")
            
            connection.commit()
            
            # 验证插入结果
            result = connection.execute(text("SELECT COUNT(*) FROM articles WHERE status = 'published' AND is_deleted = 0"))
            published_count = result.fetchone()[0]
            
            logger.info(f"✓ 成功插入 {len(test_articles)} 篇测试文章")
            logger.info(f"✓ 已发布文章数量: {published_count}")
            
            # 显示插入的文章
            result = connection.execute(text("SELECT id, title, author, category FROM articles ORDER BY id"))
            articles = result.fetchall()
            
            logger.info("\n插入的文章列表:")
            for article in articles:
                logger.info(f"  ID: {article[0]}, 标题: {article[1]}, 作者: {article[2]}, 分类: {article[3]}")
        
        logger.info("=" * 60)
        logger.info("测试数据创建完成")
        logger.info("=" * 60)
        return True
        
    except Exception as e:
        logger.error(f"创建测试数据失败: {e}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("开始创建测试数据")
    logger.info(f"创建时间: {datetime.now()}")
    
    success = create_test_articles()
    
    if success:
        logger.info("\n🎉 测试数据创建成功！")
        logger.info("现在可以测试CMS服务和API功能了。")
    else:
        logger.error("\n❌ 测试数据创建失败")

if __name__ == "__main__":
    main()
